import os
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI

# Load environment variables
load_dotenv('bin/apikey.env')

# Set environment variables
os.environ['AZURE_OPENAI_ENDPOINT'] = "https://decaideai.openai.azure.com/"
os.environ['AZURE_OPENAI_KEY'] = "5FosTEgZCbgP4tPftsCp7zzGozG4qJgelGB2JfGACfIlg4Q5FiDUJQQJ99BCACYeBjFXJ3w3AAABACOGxBJH"

print(f"AZURE_OPENAI_ENDPOINT: {os.getenv('AZURE_OPENAI_ENDPOINT')}")
print(f"AZURE_OPENAI_KEY: {'SET' if os.getenv('AZURE_OPENAI_KEY') else 'NOT SET'}")

try:
    # Initialize the model
    llm = AzureChatOpenAI(
        model="gpt-4.1",
        api_version='2025-01-01-preview',
        azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
        api_key=os.getenv('AZURE_OPENAI_KEY', ''),
    )
    
    print("LLM initialized successfully")
    
    # Test a simple call
    response = llm.invoke("Hello, how are you?")
    print(f"Response: {response}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
