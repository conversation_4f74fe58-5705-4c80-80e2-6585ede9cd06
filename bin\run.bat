@echo off
REM Simple script to run the browser-use agent on Windows

REM Navigate to the project root
cd /d "%~dp0\.."

REM Activate virtual environment
call .venv\Scripts\activate.bat

REM Set environment variables
set AZURE_OPENAI_ENDPOINT=https://decaideai.openai.azure.com/
set AZURE_OPENAI_KEY=5FosTEgZCbgP4tPftsCp7zzGozG4qJgelGB2JfGACfIlg4Q5FiDUJQQJ99BCACYeBjFXJ3w3AAABACOGxBJH

REM Run the agent
python bin\agent.py

pause
