# ========================================
# HOW TO RUN THIS AGENT:
# ========================================
# Option 1 - Terminal (EASIEST):
#   1. Open terminal in VS Code (Ctrl + `)
#   2. Copy and paste these commands one by one:
#      cd c:\Users\<USER>\browser-use
#      .venv\Scripts\activate
#      python bin\agent.py
#
# Option 2 - Run Script:
#   Just run: ./bin/run.sh
#
# Option 3 - Run Button:
#   Set Python interpreter to: c:\Users\<USER>\browser-use\.venv\Scripts\python.exe
# ========================================

from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path so imports work
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Load environment variables from apikey.env
load_dotenv(os.path.join(os.path.dirname(__file__), 'apikey.env'))

# Set environment variables directly (fallback if .env doesn't work)
if not os.getenv('AZURE_OPENAI_ENDPOINT'):
    os.environ['AZURE_OPENAI_ENDPOINT'] = "https://decaideai.openai.azure.com/"
if not os.getenv('AZURE_OPENAI_KEY'):
    os.environ['AZURE_OPENAI_KEY'] = "5FosTEgZCbgP4tPftsCp7zzGozG4qJgelGB2JfGACfIlg4Q5FiDUJQQJ99BCACYeBjFXJ3w3AAABACOGxBJH"

# Debug: Check environment variables
print(f"AZURE_OPENAI_ENDPOINT: {os.getenv('AZURE_OPENAI_ENDPOINT', 'NOT SET')}")
print(f"AZURE_OPENAI_KEY: {'SET' if os.getenv('AZURE_OPENAI_KEY') else 'NOT SET'}")

# Initialize the model
llm = AzureChatOpenAI(
    model="gpt-4.1",
    api_version='2025-01-01-preview',
    azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
    api_key=os.getenv('AZURE_OPENAI_KEY', ''),
)

# ========================================
# CHANGE THE TASK HERE:
# ========================================
# Replace the text below with whatever you want the agent to do:
# Examples:
#   "book a flight to Paris"
#   "find the best laptop under $1000 on Amazon"
#   "check the weather in New York"
#   "search for restaurants near me"
# ========================================

# Create agent with the model
agent = Agent(
    task="write an essay on google docs about the history of the internet use <NAME_EMAIL> as the email then as the password Hariom@00",  # <-- CHANGE THIS LINE
    llm=llm
)

# Run the agent 

if __name__ == "__main__":
    import asyncio
    asyncio.run(agent.run())

