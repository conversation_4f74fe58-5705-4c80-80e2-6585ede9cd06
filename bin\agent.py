from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
import os
from dotenv import load_dotenv

# Load environment variables from apikey.env
load_dotenv('bin/apikey.env')
# Also try loading from current directory
load_dotenv('apikey.env')

# Debug: Check environment variables
print(f"AZURE_OPENAI_ENDPOINT: {os.getenv('AZURE_OPENAI_ENDPOINT', 'NOT SET')}")
print(f"AZURE_OPENAI_KEY: {'SET' if os.getenv('AZURE_OPENAI_KEY') else 'NOT SET'}")

# Initialize the model
llm = AzureChatOpenAI(
    model="gpt-4.1",
    api_version='2025-01-01-preview',
    azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
    api_key=os.getenv('AZURE_OPENAI_KEY', ''),
)

# Create agent with the model
agent = Agent(
    task="find up the current price of the SandP500",
    llm=llm
)

# Run the agent
if __name__ == "__main__":
    import asyncio
    asyncio.run(agent.run())