# ========================================
# HOW TO RUN THIS AGENT:
# ========================================
# Option 1 - Terminal (EASIEST):
#   1. Open terminal in VS Code (Ctrl + `)
#   2. Copy and paste these commands one by one:
#      cd c:\Users\<USER>\browser-use
#      .venv\Scripts\activate
#      python bin\agent.py
#
# Option 2 - Run Script:
#   Just run: .\run.bat
#
# Option 3 - Run Button:
#   Set Python interpreter to: c:\Users\<USER>\browser-use\.venv\Scripts\python.exe
# ========================================

from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path so imports work
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Load environment variables from apikey.env
load_dotenv(os.path.join(os.path.dirname(__file__), 'apikey.env'))

# Set environment variables directly (fallback if .env doesn't work)
if not os.getenv('AZURE_OPENAI_ENDPOINT'):
    os.environ['AZURE_OPENAI_ENDPOINT'] = "https://decaideai.openai.azure.com/"
if not os.getenv('AZURE_OPENAI_KEY'):
    os.environ['AZURE_OPENAI_KEY'] = "5FosTEgZCbgP4tPftsCp7zzGozG4qJgelGB2JfGACfIlg4Q5FiDUJQQJ99BCACYeBjFXJ3w3AAABACOGxBJH"

# Debug: Check environment variables
print(f"AZURE_OPENAI_ENDPOINT: {os.getenv('AZURE_OPENAI_ENDPOINT', 'NOT SET')}")
print(f"AZURE_OPENAI_KEY: {'SET' if os.getenv('AZURE_OPENAI_KEY') else 'NOT SET'}")

# Initialize the model
llm = AzureChatOpenAI(
    model="gpt-4.1",
    api_version='2025-01-01-preview',
    azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
    api_key=os.getenv('AZURE_OPENAI_KEY', ''),
)

# ========================================
# CHANGE THE TASK HERE:
# ========================================
# Replace the text below with whatever you want the agent to do:
# Examples:
#   "book a flight to Paris"
#   "find the best laptop under $1000 on Amazon"
#   "check the weather in New York"
#   "search for restaurants near me"
# ========================================

# Create agent with the model
agent = Agent(
    task="INFO     [agent] 🧠 LLM call => AzureChatOpenAI [✉️ 9 msg, ~35322 tk, 41477 char, 📷 img] => JSON out + 🔨 20 tools (function_calling)
ERROR    [agent] ❌ Result failed 3/3 times:
 Error code: 400 - {'error': {'inner_error': {'code': 'ResponsibleAIPolicyViolation', 'content_filter_results': {'jailbreak': {'filtered': True, 'detected': True}}}, 'code': 'content_filter', 'message': "The response was filtered due to the prompt triggering Azure OpenAI's content management policy. Please modify your prompt and retry. To learn more about our content filtering policies please read our documentation: \r\nhttps://go.microsoft.com/fwlink/?linkid=2198766.", 'param': 'prompt', 'type': None}}    
INFO     [agent] 📍 Step 1: Ran 1 actions in 1.39s: ❌ 1        
ERROR    [agent] ❌ Stopping due to 3 consecutive failures      
INFO     [browser] 🛑 Stopped the chromium browser keep_alive=False user_data_dir=~\.config\browseruse\profiles\default cdp_url=None pid=14848
Exception ignored in: <function BaseSubprocessTransport.__del__ at 0x000002C5BCED8B80>
Traceback (most recent call last):
-windows-x86_64-none\Lib\asyncio\base_subprocess.py", line 126, in __del__
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\asyncio\base_subprocess.py", line 104, in close
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\asyncio\proactor_events.py", line 109, in close
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\asyncio\base_events.py", line 762, in call_soon
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\asyncio\base_events.py", line 520, in _check_closed
RuntimeError: Event loop is closed
Press any key to continue . . .
 ",  # <-- CHANGE THIS LINE
    llm=llm,
    enable_memory=False  # Disable memory so it starts fresh each time
)

# Run the agent 

if __name__ == "__main__":
    import asyncio
    asyncio.run(agent.run())

