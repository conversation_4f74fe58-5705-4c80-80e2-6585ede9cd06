#!/usr/bin/env python3
"""
Login Setup Script
Run this first to login to websites, then run the main agent.

Usage: python login_setup.py
"""

from browser_use import BrowserSession
import asyncio
import time

async def setup_logins():
    print("🚀 Opening browser for login setup...")
    print("📝 Login to these sites:")
    print("   - LinkedIn")
    print("   - Indeed") 
    print("   - Glassdoor")
    print("   - Company career pages")
    print("   - Any other job sites")
    print("\n⏰ Press Ctrl+C when done logging in")
    
    browser_session = BrowserSession(
        executable_path=r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        user_data_dir=r'C:\Users\<USER>\.config\browseruse\profiles\default',
        headless=False,
        keep_alive=True
    )
    
    try:
        # Start browser session
        await browser_session.start()
        
        # Navigate to LinkedIn first
        page = await browser_session.get_current_page()
        await page.goto("https://www.linkedin.com")
        
        print("✅ Browser opened! Login to your accounts now.")
        print("🔄 The browser will stay open until you press Ctrl+C")
        
        # Keep running until user stops
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n✅ Login setup complete!")
        print("🚀 Now run: .\\run.bat")
        
    finally:
        await browser_session.close()

if __name__ == "__main__":
    asyncio.run(setup_logins())
