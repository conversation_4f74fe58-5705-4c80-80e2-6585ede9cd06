#!/usr/bin/env python3
"""
Login Setup Script - Simple Chrome Launcher
Run this first to login to websites, then run the main agent.

Usage: python login_setup.py
"""

import subprocess
import time
import os

def setup_logins():
    print("🚀 Opening Chrome for login setup...")
    print("📝 Login to these sites:")
    print("   - LinkedIn")
    print("   - Indeed")
    print("   - Glassdoor")
    print("   - Company career pages")
    print("   - Any other job sites")
    print("\n⏰ Close the browser when done logging in")

    # Create the profile directory if it doesn't exist
    profile_dir = r'C:\Users\<USER>\.config\browseruse\profiles\default'
    os.makedirs(profile_dir, exist_ok=True)

    # Launch Chrome with the same profile the agent will use
    chrome_cmd = [
        r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        f'--user-data-dir={profile_dir}',
        '--no-first-run',
        '--no-default-browser-check',
        'https://www.linkedin.com'
    ]

    try:
        print("✅ Launching Chrome...")
        subprocess.run(chrome_cmd)
        print("✅ Chrome closed!")
        print("🚀 Your logins are saved. Now run: .\\run.bat")

    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Try running this command manually:")
        print(f'   "{chrome_cmd[0]}" --user-data-dir="{profile_dir}" https://www.linkedin.com')

if __name__ == "__main__":
    setup_logins()
