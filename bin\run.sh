#!/bin/bash
# Simple script to run the browser-use agent

# Navigate to the project root
cd "$(dirname "$0")/.."

# Activate virtual environment
source .venv/Scripts/activate

# Set environment variables
export AZURE_OPENAI_ENDPOINT="https://decaideai.openai.azure.com/"
export AZURE_OPENAI_KEY="5FosTEgZCbgP4tPftsCp7zzGozG4qJgelGB2JfGACfIlg4Q5FiDUJQQJ99BCACYeBjFXJ3w3AAABACOGxBJH"

# Run the agent
python bin/agent.py
